#ifndef  SYSTEM_INIT_H
#define  SYSTEM_INIT_H
#include "zf_common_headfile.h"

//  #define PWM_CH1                 (PWM2_MODULE0_CHA_C6)
//	#define PWM_CH2                 (PWM2_MODULE1_CHA_C8)
//	#define PWM_CH3                 (PWM2_MODULE2_CHB_C11)
//	#define PWM_CH4                 (PWM2_MODULE3_CHB_D3)
//	#define PWM_CH1                 (PWM2_MODULE0_CHA_C6)
//	#define PWM_CH2                 (PWM2_MODULE1_CHA_C8)
//  #define PWM_CH3                 (PWM2_MODULE3_CHA_D2)
//	#define PWM_CH4                 (PWM2_MODULE2_CHA_C10)
	
//	#define PWM_CH1             	(PWM2_MODULE2_CHA_C10)
//	#define PWM_CH2            		(PWM2_MODULE3_CHA_D2)
//	#define PWM_CH3               	(PWM2_MODULE0_CHA_C6)  
//	#define PWM_CH4              	(PWM2_MODULE1_CHA_C8) 

	#define PWM_CH1             (PWM2_MODULE0_CHA_C6)
	#define PWM_CH2             (PWM2_MODULE0_CHB_C7)
	#define PWM_CH3             (PWM2_MODULE2_CHB_C11)  

	#define ENCODER_CH2                 (QTIMER1_ENCODER2)
	#define ENCODER_CH1                 (QTIMER1_ENCODER1)
	#define ENCODER_CH3                 (QTIMER2_ENCODER2)

void global_init(void);

#endif  /*SYSTEM_INIT_H*/