/********************************************************************************
* @File name: vofa+.c
* @Author: Flexible
* @Version: 3.0
* @Date: 2024-01-19
* @Description: 上位机VOFA+的pid调参通讯协议
********************************************************************************/

#include  "vofa.h"
#include  "math.h"


uint8_t tail[4] = {0x00, 0x00, 0x80, 0x7F};	//vofa+ JUSTFALOT通信协议针尾
uint8_t tx_data[4];//中间转换类型存储需要



//定义下位机向vofa发送的数据直接赋值即可

float data_2 ;
float data_3 ;
float data_4 ;
float data_5 ;
float data_6 ;
float data_7 ;



/*
 * @brief 把float类型的数据转换成字符型（char）的数据
 * @param float f		输入float类型的数据
 * @param uint8_t * c	输入char类型的数据
 * @see  float_turn_u8(data_1,c_data) ;
 * @return 返回说明
 */
void float_turn_u8(float f,uint8_t * c)//563.2    5
{
	uint8_t x;
	FloatLongType data;
	data.fdata=f;
	
	for(x=0;x<4;x++)
        {
		c[x]=(uint8_t)(data.ldata>>(x*8));
	}
}


void VOFA_JustFloat(void) //此函数放在中断或主函数中循环调用

{

//float data_1 =encoder_real[3]*1.0;

 
data_4=3;
data_5=4;
data_6=5;
data_7=6;
//float_turn_u8(data_1,tx_data) ;//将flot 数据，转化为4个字节
//uart_write_buffer(UART_8,tx_data , sizeof(tx_data));//发送 4个字节至上位机

//下面同理	
float_turn_u8(data_2,tx_data) ;
uart_write_buffer(UART_1,tx_data, sizeof(tx_data));

float_turn_u8(data_3,tx_data) ;
uart_write_buffer(UART_1,tx_data, sizeof(tx_data));

float_turn_u8(data_4,tx_data) ;
uart_write_buffer(UART_1,tx_data, sizeof(tx_data));
//	
float_turn_u8(data_5,tx_data) ;
uart_write_buffer(UART_1,tx_data, sizeof(tx_data));
//	
//float_turn_u8(data_6,tx_data) ;    
//uart_write_buffer(UART_8,tx_data, sizeof(tx_data));
// 

//float_turn_u8(data_7,tx_data) ;    
//uart_write_buffer(UART_8,tx_data, sizeof(tx_data));


uart_write_buffer(UART_1,tail, sizeof(tail));//这是vofa+,JUSTFALOT协议规定的zhenwei

}
