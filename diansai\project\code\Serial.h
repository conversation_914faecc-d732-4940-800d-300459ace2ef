 

 #ifndef __SERIAL_H
#define __SERIAL_H

#include "zf_common_headfile.h"
 

#define UART_BUFFER_SIZE 32//���￼�ǵ����ݰ�ֻ����3������(x,y,class)��������ݰ����������ݽ϶�����ʵ����ӻ�����size
extern uint8 packge1_finish_flag, packge4_finish_flag; 
extern uint8 uart1_buffer[UART_BUFFER_SIZE], uart4_buffer[UART_BUFFER_SIZE];
extern float uart4_data_arr[8]; 
extern uint32 uart1_data_arr[8]  ; 
void my_uart_init();
extern float w,y;
void openart_retrieval_check(void);
extern  void uart1_send_number1(void);
extern int cn;
extern int finish;
void test();
void send();
extern int a;
extern int w_int, y_int;
extern uint8 data_valid_flag;
#endif
 
