./objects/zf_device_scc8660_flexio.o: \
  ..\..\libraries\zf_device\zf_device_scc8660_flexio.c \
  ..\..\libraries\zf_common\zf_common_debug.h \
  ..\..\libraries\zf_common\zf_common_typedef.h \
  ..\..\libraries\sdk\drives\fsl_common.h \
  ..\..\libraries\sdk\deceive\fsl_device_registers.h \
  ..\..\libraries\sdk\deceive\MIMXRT1064.h \
  ..\..\libraries\sdk\CMSIS\Include\core_cm7.h \
  ..\..\libraries\sdk\deceive\system_MIMXRT1064.h \
  ..\..\libraries\sdk\deceive\MIMXRT1064_features.h \
  ..\..\libraries\sdk\drives\fsl_common_arm.h \
  ..\..\libraries\sdk\drives\fsl_clock.h \
  ..\..\libraries\zf_common\zf_common_interrupt.h \
  ..\..\libraries\zf_driver\zf_driver_delay.h \
  ..\..\libraries\zf_driver\zf_driver_exti.h \
  ..\..\libraries\zf_driver\zf_driver_gpio.h \
  ..\..\libraries\sdk\drives\fsl_gpio.h \
  ..\..\libraries\zf_driver\zf_driver_uart.h \
  ..\..\libraries\sdk\drives\fsl_lpuart.h \
  ..\..\libraries\zf_driver\zf_driver_soft_iic.h \
  ..\..\libraries\zf_device\zf_device_camera.h \
  ..\..\libraries\zf_common\zf_common_fifo.h \
  ..\..\libraries\zf_device\zf_device_type.h \
  ..\..\libraries\zf_driver\zf_driver_flexio_csi.h \
  ..\..\libraries\sdk\drives\fsl_edma.h \
  ..\..\libraries\zf_device\zf_device_config.h \
  ..\..\libraries\zf_device\zf_device_scc8660_flexio.h
