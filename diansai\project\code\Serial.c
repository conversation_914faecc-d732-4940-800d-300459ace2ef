 #include "zf_common_headfile.h"

// 串口4用于90度摄像头分类，串口1用于130度摄像头目标检测
static  fifo_struct     uart1_fifo,  uart4_fifo;
uint8   uart1_buffer[UART_BUFFER_SIZE];    // 数据存放数组
uint8   uart4_buffer[UART_BUFFER_SIZE];
static  uint8           uart_data   = 0;
static  uint8 uart1_rx_state,  uart4_rx_state= 0;
uint8   packge1_finish_flag = 0, packge4_finish_flag = 0; //数据包接收完成标志
static  uint32 length1 = 0, length4; //fifo实际的缓存数据长度
 
uint32 uart1_data_arr[8] = {0};  
float uart4_data_arr[8] = {-1};  
 uint8 uart_get_data[32];       // 串口接收数据缓冲区
uint8 uart_set_data[32]; 
uint8 fifo_get_data[32];    // fifo 输出读出缓冲区
uint8 fifo_set_data[32]; 
#define UART_INDEX              (UART_1  )                             
#define UART_BAUDRATE           (115200)                                
#define UART_TX_PIN             (UART1_TX_B12  )                        
#define UART_RX_PIN             (UART1_RX_B13  )                        
#define UART_PRIORITY           (LPUART1_IRQn)                 
uint32 fifo_data_count = 0;                         // fifo 数据个数
int cn;
int finish=0;
void my_uart_init()
{
     fifo_init(&uart1_fifo, FIFO_DATA_8BIT, uart_get_data, 64);              // 初始化 fifo 挂载缓冲区
     fifo_init(&uart4_fifo, FIFO_DATA_8BIT, uart_set_data, 64);  
     uart_init(UART_INDEX, UART_BAUDRATE, UART_TX_PIN, UART_RX_PIN); 
 	   uart_init(UART_4,115200,UART4_TX_C16,UART4_RX_C17);// 初始化串口
    uart_rx_interrupt(UART_INDEX, 1); 
    uart_rx_interrupt(UART_4, 1);  	
 
//    uart_tx_interrupt(UART_INDEX, 1);     	 
    interrupt_set_priority(LPUART1_IRQn,0);                       // 设置对应 UART_INDEX 的中断优先级为 0
//    interrupt_set_priority(UART_PRIORITY, 1); 
	  interrupt_set_priority(LPUART4_IRQn, 1); 
    interrupt_global_enable (0);
}


//-----------------------------------------------------------------------------------------------
// 函数简介  串口中断回调函数
// 参数说明  uart_n：串口号
// 返回参数  void  
float w,y;
void my_uart_callback(uart_index_enum uart_n)
{
     switch (uart_n)
    {
        //注意：串口1在与ART传输时一定要断开DAP下载线！！！，否则会接收不到
        //注意：串口1在与ART传输时一定要断开DAP下载线！！！，否则会接收不到
        //注意：串口1在与ART传输时一定要断开DAP下载线！！！，否则会接收不到
        case UART_1:
        {  
            if(uart_query_byte(UART_1, &uart_data) != 0)
            {
                if(uart1_rx_state == 0 && uart_data == '@')      //接收到包头
                {  
                    if(fifo_used(&uart1_fifo)==0) {y=2;uart1_rx_state = 1;packge1_finish_flag = 0;}
                }
                else if(uart1_rx_state == 1 && uart_data != '\n') //接收到包头但未接收到包尾时正常写入数据到fifo
                { 
                    fifo_write_buffer(&uart1_fifo, &uart_data, 1);
                }
                else if(uart1_rx_state == 1 && uart_data == '\n') //接收到包尾
                { 
                    fifo_write_buffer(&uart1_fifo, "\n", 1);
                    uart1_rx_state = 0;
                    length1 = fifo_used(&uart1_fifo);
                    if(length1>=1)//正常数据的最短长度（x + ',' + y + ',' + distance + correct_flag  + '\n'>=7）， 如果比这个长度还短就不读取
                    {   
                        sscanf((const char*)uart_get_data, "%d\n", &uart1_data_arr[0]  );
                    }
                    fifo_clear(&uart1_fifo);
                    packge1_finish_flag = 1; 
                }
            }
            break;
        }
        case UART_4:
        {
           if(uart_query_byte(UART_4, &uart_data) != 0)
            {
                if(uart4_rx_state == 0 && uart_data == '@')      //接收到包头
                {   
                    if(fifo_used(&uart4_fifo)==0) {uart4_rx_state = 1;packge4_finish_flag = 0;}
                }
                else if(uart4_rx_state == 1 && uart_data != '\n'&&finish==0) //接收到包头但未接收到包尾时正常写入数据到fifo
                {   
                    fifo_write_buffer(&uart4_fifo, &uart_data, 1);
                }
                else if(uart4_rx_state == 1 && uart_data == '\n'&&finish==0) //接收到包尾
                { 
                    fifo_write_buffer(&uart4_fifo, "\n", 1);
                    uart4_rx_state = 0;
                    length4 = fifo_used(&uart4_fifo);
                    if(length4>=1)//正常数据的最短长度（x + ',' + y + ',' + distance + correct_flag  + '\n'>=7）， 如果比这个长度还短就不读取
                    {   
                        sscanf((const char*)uart_set_data, "%f\n", &uart4_data_arr[0]  );
											 finish=0;
										 
                    }
                    fifo_clear(&uart4_fifo);
                    packge4_finish_flag = 1; 
                }
								else if(uart4_rx_state == 0 && uart_data == '#')      //接收到包头
                { 
                    if(fifo_used(&uart4_fifo)==0) {uart4_rx_state = 1;packge4_finish_flag = 0;  finish=1;}
                }
                else if(uart4_rx_state == 1 && uart_data != '\n'&&finish==1) //接收到包头但未接收到包尾时正常写入数据到fifo
                {   
                    fifo_write_buffer(&uart4_fifo, &uart_data, 1);
                }
                else if(uart4_rx_state == 1 && uart_data == '\n'&&finish==1) //接收到包尾
                { 
                    fifo_write_buffer(&uart4_fifo, "\n", 1);
                    uart4_rx_state = 0;
                    length4 = fifo_used(&uart4_fifo);
                    if(length4>=1)//正常数据的最短长度（x + ',' + y + ',' + distance + correct_flag  + '\n'>=7）， 如果比这个长度还短就不读取
                    {   
                        sscanf((const char*)uart_set_data, "%f\n", &uart4_data_arr[1]  );
											  finish=0;
										 
                    }
                    fifo_clear(&uart4_fifo);
                    packge4_finish_flag = 1; 
                }
            }
						 
            break;
						
        }

    }
    memset(&uart_data, 0, 1);
}
 


int a;


int w_int, y_int; 

// 添加数据有效性标志
uint8 data_valid_flag = 0;

void send(){
    // 检查数据是否有效
    if(!data_valid_flag) return;

    a=uart1_data_arr[0];

    // 使用临时变量避免中断期间数据被修改
    float temp_uart4_0, temp_uart4_1;
    uint32 temp_uart1_0;

    // 关闭中断，确保数据一致性
    interrupt_global_enable(0);
    temp_uart4_0 = uart4_data_arr[0];
    temp_uart4_1 = uart4_data_arr[1];
    temp_uart1_0 = uart1_data_arr[0];
    interrupt_global_enable(1);

    w_int = (int)(100 * temp_uart4_0);
    y_int = (int)(100 * temp_uart4_1);

    // 只有当摄像头检测到目标时才发送数据
    if(temp_uart1_0 == 1){
        printf("index.n2.val=%d\xff\xff\xff",w_int);//距离
        printf("index.x2.val=%d\xff\xff\xff",y_int);//边长
    }
}















