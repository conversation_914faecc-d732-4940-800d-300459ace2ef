#ifndef  SMOTOR_H
#define  SMOTOR_H

#include "zf_common_headfile.h" 
extern uint8 c27_grab;

extern int servo_1,servo_2;
void servo_slow_ctrl(uint16 _servo1_angle, uint16 _servo2_angle, float _step_count);
extern uint8 servo_finish; 
extern uint8 five_class_finish;
extern uint8 three_put_finish;
extern uint8 five_put_finish;
extern uint8 five_put_count;
void Servo();
void Servo1(void);
void Servo_zhuaqu1();
void Servo_zhuaqu2();
void Servo_fangzhi1();
void Servo_fangzhi2();
#endif